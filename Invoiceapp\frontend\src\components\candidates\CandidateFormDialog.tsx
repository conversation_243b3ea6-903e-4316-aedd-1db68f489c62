import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import candidateService from "@/services/candidateService";
import { clientService, Client } from "@/services/clientService";
import { projectService, Project } from "@/services/projectService";
// We don't need useNavigate anymore
// import { useNavigate } from "react-router-dom";

interface CandidateFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  candidateId?: string;
  defaultValues?: {
    name: string;
    email: string;
    phone: string;
    position: string;
    status: string;
    skills: string;
    experience: string;
    notes: string;
    clientId?: string;
    projectId?: string;
    joiningDate?: string;
    billingRate?: string;
    panNo?: string;
    aadharNo?: string;
    uanNo?: string;
    bankAccountNo?: string;
    branchName?: string;
    ifscCode?: string;
    address?: string;
    salaryOffered?: string;
  };
  onSuccess?: () => void;
}

const CandidateFormDialog: React.FC<CandidateFormDialogProps> = ({
  open,
  onOpenChange,
  candidateId,
  defaultValues,
  onSuccess,
}) => {
  const isEditing = !!candidateId;

  // State for clients
  const [clients, setClients] = useState<Client[]>([]);
  const [clientsLoading, setClientsLoading] = useState(true);
  const [clientsError, setClientsError] = useState<Error | null>(null);

  // State for projects
  const [projects, setProjects] = useState<Project[]>([]);
  const [projectsLoading, setProjectsLoading] = useState(true);
  const [projectsError, setProjectsError] = useState<Error | null>(null);

  // State to track filtered projects based on selected client
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    position: "",
    status: "New",
    skills: "",
    experience: "",
    notes: "",
    clientId: "",
    projectId: "",
    joiningDate: "",
    billingRate: "",
    panNo: "",
    aadharNo: "",
    uanNo: "",
    bankAccountNo: "",
    branchName: "",
    ifscCode: "",
    address: "",
    salaryOffered: ""
  });

  // We don't need navigate anymore since we're using the onSuccess callback
  // const navigate = useNavigate();

  // Fetch clients when component mounts
  useEffect(() => {
    const fetchClients = async () => {
      try {
        setClientsLoading(true);
        setClientsError(null);
        console.log('Fetching clients for candidate form...');
        const data = await clientService.getAllClients();
        console.log('Clients fetched successfully:', data);
        setClients(data);
      } catch (error) {
        console.error('Error fetching clients:', error);
        setClientsError(error as Error);
        toast.error('Failed to load clients. Please try again.');
      } finally {
        setClientsLoading(false);
      }
    };

    fetchClients();
  }, []);

  // Fetch projects when component mounts
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setProjectsLoading(true);
        setProjectsError(null);
        console.log('Fetching projects for candidate form...');
        const data = await projectService.getAllProjects();
        console.log('Projects fetched successfully:', data);

        if (Array.isArray(data) && data.length > 0) {
          setProjects(data);
        } else {
          console.warn('No projects returned from API, using fallback data');
          // Fallback data in case the API doesn't return anything
          const fallbackProjects = [
            { id: 1, name: "Project A", clientId: 1 },
            { id: 2, name: "Project B", clientId: 1 },
            { id: 3, name: "Project C", clientId: 2 },
            { id: 4, name: "Project D", clientId: 3 }
          ];
          setProjects(fallbackProjects);

          // Show a warning to the user
          toast.warning('Using sample project data. API may not be available.');
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
        setProjectsError(error as Error);
        toast.error('Failed to load projects. Please try again.');

        // Set fallback data even on error
        const fallbackProjects = [
          { id: 1, name: "Project A", clientId: 1 },
          { id: 2, name: "Project B", clientId: 1 },
          { id: 3, name: "Project C", clientId: 2 },
          { id: 4, name: "Project D", clientId: 3 }
        ];
        setProjects(fallbackProjects);
      } finally {
        setProjectsLoading(false);
      }
    };

    fetchProjects();
  }, []);

  // Filter projects when client selection changes
  useEffect(() => {
    if (formData.clientId && projects.length > 0) {
      console.log(`Filtering projects for client ID: ${formData.clientId}`);
      const clientProjects = projects.filter(
        project => project.clientId === parseInt(formData.clientId, 10)
      );
      console.log('Filtered projects:', clientProjects);
      setFilteredProjects(clientProjects);
    } else {
      // If no client is selected, show all projects
      setFilteredProjects(projects);
    }
  }, [formData.clientId, projects]);

  // Reset form data when dialog opens/closes or when defaultValues change
  useEffect(() => {
    if (open) {
      if (defaultValues) {
        // When editing, populate with existing data
        console.log("Populating candidate form with data:", defaultValues);
        setFormData({
          name: defaultValues.name || "",
          email: defaultValues.email || "",
          phone: defaultValues.phone || "",
          clientId: defaultValues.clientId || "",
          projectId: defaultValues.projectId || "",
          staffingTypeId: defaultValues.staffingTypeId || "",
          rate: defaultValues.rate || "",
          billingType: defaultValues.billingType || "monthly",
          ...defaultValues
        });
      } else {
        // When creating new, reset to empty form
        setFormData({
          name: "",
          email: "",
          phone: "",
          clientId: "",
          projectId: "",
          staffingTypeId: "",
          rate: "",
          billingType: "monthly"
        });
      }
    }
  }, [open, defaultValues]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.name || !formData.email || !formData.position) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Show loading toast
    const loadingToast = toast.loading(isEditing ? "Updating candidate..." : "Adding candidate...");

    try {
      // Prepare data for API submission
      // Find the selected client and project objects
      const selectedClientId = parseInt(formData.clientId) || null;
      const selectedProjectId = parseInt(formData.projectId) || null;

      // Find the selected client from the clients array
      const selectedClient = selectedClientId
        ? clients.find(client => client.id === selectedClientId) || { id: selectedClientId, name: "Unknown Client" }
        : null;

      // Find the selected project from the projects array
      const selectedProject = selectedProjectId
        ? projects.find(project => project.id === selectedProjectId) || { id: selectedProjectId, name: "Unknown Project" }
        : null;

        // Create a clean project object without any extra properties
        const projectWithoutClient = selectedProject ? {
          ...selectedProject,
          // Explicitly cast to any to avoid TypeScript errors with property deletion
        } as any : null;

        if (projectWithoutClient) {
          // Remove any properties that might exist but aren't needed
          if ('client' in projectWithoutClient) delete projectWithoutClient.client;
          if ('bdm' in projectWithoutClient) delete projectWithoutClient.bdm;
          if ('hsnCode' in projectWithoutClient) delete projectWithoutClient.hsnCode;
          if ('created_at' in projectWithoutClient) delete projectWithoutClient.created_at;
          if ('updated_at' in projectWithoutClient) delete projectWithoutClient.updated_at;
        }

      // Create a simple project object with just the required fields
      let cleanProject = null;
      if (projectWithoutClient) {
        cleanProject = {
          id: projectWithoutClient.id,
          name: projectWithoutClient.name
        };

        // Add clientId if it exists
        if ('clientId' in projectWithoutClient) {
          (cleanProject as any).clientId = projectWithoutClient.clientId;
        }
      }

      const candidateData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        designation: formData.position,
        status: formData.status,
        clientId: selectedClientId,
        projectId: selectedProjectId,
        // Add client and project objects with their data
        client: selectedClient ? {
          id: selectedClient.id,
          name: selectedClient.name
        } : null,
        project: cleanProject,
        // Format joining date properly - handle both new dates (YYYY-MM-DD) and existing ISO dates
        joiningDate: formData.joiningDate ?
          (formData.joiningDate.includes('T') ? formData.joiningDate : formData.joiningDate + "T09:06:55.078Z")
          : null,
        billingRate: formData.billingRate ? parseInt(formData.billingRate) : null,
        panNo: formData.panNo || null,
        aadharNo: formData.aadharNo || null,
        uanNo: formData.uanNo || null,
        experienceInYrs: formData.experience ? parseInt(formData.experience) : null,
        bankAccountNo: formData.bankAccountNo || null,
        branchName: formData.branchName || null,
        ifscCode: formData.ifscCode || null,
        address: formData.address || null,
        salaryOffered: formData.salaryOffered ? parseInt(formData.salaryOffered) : null,
        skills: formData.skills || null,
        notes: formData.notes || null
      };

      console.log('Submitting candidate data:', candidateData);

      if (isEditing && candidateId) {
        // API call to update candidate
        await candidateService.updateCandidate(candidateId, candidateData);
        toast.success(`Candidate "${formData.name}" updated successfully`);
      } else {
        // API call to create candidate
        await candidateService.createCandidate(candidateData);
        toast.success(`Candidate "${formData.name}" added successfully`);
      }

      // Call onSuccess callback if provided to refresh the candidates list
      if (onSuccess) {
        onSuccess();
      }

      // Close the dialog
      onOpenChange(false);

      // Dismiss loading toast
      toast.dismiss(loadingToast);
    } catch (error) {
      console.error('Error saving candidate:', error);

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show error toast
      toast.error(isEditing
        ? `Failed to update candidate. Please try again.`
        : `Failed to add candidate. Please try again.`
      );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden p-0">
        <DialogHeader className="px-6 pt-6 pb-2">
          <DialogTitle className="text-xl">{isEditing ? "Edit Candidate" : "Add New Candidate"}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6 py-4 max-h-[70vh] overflow-y-auto px-6">
          <div className="border-b pb-4 mb-4">
            <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">Basic Information</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Enter candidate name"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="Enter email address"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="Enter phone number"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="position">Designation *</Label>
                <Input
                  id="position"
                  name="position"
                  value={formData.position}
                  onChange={handleChange}
                  placeholder="Enter designation"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleSelectChange("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="New">New</SelectItem>
                    <SelectItem value="Active">Active</SelectItem>
                    <SelectItem value="Inactive">Inactive</SelectItem>
                    <SelectItem value="Onboarding">Onboarding</SelectItem>
                    <SelectItem value="Terminated">Terminated</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="experience">Experience (in years)</Label>
                <Input
                  id="experience"
                  name="experience"
                  type="number"
                  step="0.1"
                  value={formData.experience}
                  onChange={handleChange}
                  placeholder="Enter years of experience"
                />
              </div>
            </div>
          </div>

          <div className="border-b pb-4 mb-4">
            <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">Project Assignment</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="clientId">Client</Label>
                <Select
                  value={formData.clientId}
                  onValueChange={(value) => handleSelectChange("clientId", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select client" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px] overflow-y-auto">
                    {clientsLoading ? (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        <span>Loading clients...</span>
                      </div>
                    ) : clients.length > 0 ? (
                      clients.map((client) => (
                        <SelectItem key={client.id} value={String(client.id)}>
                          {client.name}
                        </SelectItem>
                      ))
                    ) : (
                      <div className="p-2 text-center text-gray-500">
                        {clientsError ? "Error loading clients" : "No clients found"}
                      </div>
                    )}

                    {clientsError && (
                      <div className="p-2 border-t">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={() => {
                            setClientsLoading(true);
                            clientService.getAllClients()
                              .then(data => {
                                setClients(data);
                                setClientsError(null);
                              })
                              .catch(error => setClientsError(error as Error))
                              .finally(() => setClientsLoading(false));
                          }}
                        >
                          Retry loading clients
                        </Button>
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="projectId">Project</Label>
                <Select
                  value={formData.projectId}
                  onValueChange={(value) => handleSelectChange("projectId", value)}
                  disabled={!formData.clientId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={formData.clientId ? "Select project" : "Select client first"} />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px] overflow-y-auto">
                    {!formData.clientId ? (
                      <div className="p-2 text-center text-gray-500">
                        Please select a client first
                      </div>
                    ) : projectsLoading ? (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        <span>Loading projects...</span>
                      </div>
                    ) : filteredProjects.length > 0 ? (
                      filteredProjects.map((project) => (
                        <SelectItem key={project.id} value={String(project.id)}>
                          {project.name || `Project ${project.id}`}
                        </SelectItem>
                      ))
                    ) : (
                      <div className="p-2 text-center text-gray-500">
                        {projectsError ? "Error loading projects" : "No projects found for this client"}
                      </div>
                    )}

                    {/* Debug info - remove in production */}
                    <div className="p-2 border-t text-xs text-gray-500">
                      <div>Client ID: {formData.clientId || 'None'}</div>
                      <div>Total Projects: {projects.length}</div>
                      <div>Filtered Projects: {filteredProjects.length}</div>
                    </div>

                    {projectsError && (
                      <div className="p-2 border-t">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={() => {
                            setProjectsLoading(true);
                            projectService.getAllProjects()
                              .then(data => {
                                setProjects(data);
                                setProjectsError(null);
                              })
                              .catch(error => setProjectsError(error as Error))
                              .finally(() => setProjectsLoading(false));
                          }}
                        >
                          Retry loading projects
                        </Button>
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="joiningDate">Joining Date</Label>
                <Input
                  id="joiningDate"
                  name="joiningDate"
                  type="date"
                  value={formData.joiningDate}
                  onChange={handleChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="billingRate">Billing Rate</Label>
                <Input
                  id="billingRate"
                  name="billingRate"
                  type="number"
                  step="0.01"
                  value={formData.billingRate}
                  onChange={handleChange}
                  placeholder="Enter billing rate"
                />
              </div>
            </div>
          </div>

          <div className="border-b pb-4 mb-4">
            <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">Personal Information</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="panNo">PAN Number</Label>
                <Input
                  id="panNo"
                  name="panNo"
                  value={formData.panNo}
                  onChange={handleChange}
                  placeholder="Enter PAN number"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="aadharNo">Aadhar Number</Label>
                <Input
                  id="aadharNo"
                  name="aadharNo"
                  value={formData.aadharNo}
                  onChange={handleChange}
                  placeholder="Enter Aadhar number"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="uanNo">UAN Number</Label>
                <Input
                  id="uanNo"
                  name="uanNo"
                  value={formData.uanNo}
                  onChange={handleChange}
                  placeholder="Enter UAN number"
                />
              </div>

              <div className="space-y-2 col-span-2">
                <Label htmlFor="address">Address</Label>
                <Textarea
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  placeholder="Enter address"
                  className="min-h-[80px]"
                />
              </div>
            </div>
          </div>

          <div className="border-b pb-4 mb-4">
            <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">Banking Details</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bankAccountNo">Bank Account Number</Label>
                <Input
                  id="bankAccountNo"
                  name="bankAccountNo"
                  value={formData.bankAccountNo}
                  onChange={handleChange}
                  placeholder="Enter bank account number"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="branchName">Branch Name</Label>
                <Input
                  id="branchName"
                  name="branchName"
                  value={formData.branchName}
                  onChange={handleChange}
                  placeholder="Enter branch name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="ifscCode">IFSC Code</Label>
                <Input
                  id="ifscCode"
                  name="ifscCode"
                  value={formData.ifscCode}
                  onChange={handleChange}
                  placeholder="Enter IFSC code"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="salaryOffered">Salary Offered</Label>
                <Input
                  id="salaryOffered"
                  name="salaryOffered"
                  type="number"
                  step="0.01"
                  value={formData.salaryOffered}
                  onChange={handleChange}
                  placeholder="Enter salary offered"
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="skills">Skills</Label>
            <Textarea
              id="skills"
              name="skills"
              value={formData.skills}
              onChange={handleChange}
              placeholder="Enter candidate skills (e.g. JavaScript, React, Node.js)"
              className="min-h-[80px]"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              placeholder="Enter any additional notes"
              className="min-h-[80px]"
            />
          </div>

          <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0 px-6 py-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="w-full sm:w-auto order-2 sm:order-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="w-full sm:w-auto order-1 sm:order-2"
            >
              {isEditing ? "Update Candidate" : "Add Candidate"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CandidateFormDialog;
