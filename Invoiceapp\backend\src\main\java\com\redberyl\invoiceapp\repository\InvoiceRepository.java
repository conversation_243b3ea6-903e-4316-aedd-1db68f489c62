package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.enums.InvoiceStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface InvoiceRepository extends JpaRepository<Invoice, Long> {
    Optional<Invoice> findByInvoiceNumber(String invoiceNumber);
    List<Invoice> findByClientId(Long clientId);
    List<Invoice> findByProjectId(Long projectId);
    List<Invoice> findByCandidateId(Long candidateId);
    List<Invoice> findByInvoiceDateBetween(LocalDate startDate, LocalDate endDate);
    List<Invoice> findByDueDateBefore(LocalDate date);
    List<Invoice> findByIsRecurring(Boolean isRecurring);
    List<Invoice> findByPublishedToFinance(Boolean publishedToFinance);
    List<Invoice> findByStatus(InvoiceStatus status);

    /**
     * Find invoice by ID with all related entities eagerly loaded
     */
    @Query("SELECT i FROM Invoice i " +
           "LEFT JOIN FETCH i.client c " +
           "LEFT JOIN FETCH i.project p " +
           "LEFT JOIN FETCH p.bdm " +
           "LEFT JOIN FETCH p.managerSpoc " +
           "LEFT JOIN FETCH p.accountHeadSpoc " +
           "LEFT JOIN FETCH p.businessHeadSpoc " +
           "LEFT JOIN FETCH p.hrSpoc " +
           "LEFT JOIN FETCH p.financeSpoc " +
           "LEFT JOIN FETCH i.candidate cand " +
           "LEFT JOIN FETCH i.hsnCode " +
           "LEFT JOIN FETCH i.redberylAccount " +
           "LEFT JOIN FETCH i.invoiceType " +
           "LEFT JOIN FETCH i.staffingType " +
           "WHERE i.id = :id")
    Optional<Invoice> findByIdWithAllRelations(@Param("id") Long id);

    /**
     * Find invoice by invoice number with all related entities eagerly loaded
     */
    @Query("SELECT i FROM Invoice i " +
           "LEFT JOIN FETCH i.client c " +
           "LEFT JOIN FETCH i.project p " +
           "LEFT JOIN FETCH p.bdm " +
           "LEFT JOIN FETCH p.managerSpoc " +
           "LEFT JOIN FETCH p.accountHeadSpoc " +
           "LEFT JOIN FETCH p.businessHeadSpoc " +
           "LEFT JOIN FETCH p.hrSpoc " +
           "LEFT JOIN FETCH p.financeSpoc " +
           "LEFT JOIN FETCH i.candidate cand " +
           "LEFT JOIN FETCH i.hsnCode " +
           "LEFT JOIN FETCH i.redberylAccount " +
           "LEFT JOIN FETCH i.invoiceType " +
           "LEFT JOIN FETCH i.staffingType " +
           "WHERE i.invoiceNumber = :invoiceNumber")
    Optional<Invoice> findByInvoiceNumberWithAllRelations(@Param("invoiceNumber") String invoiceNumber);

    /**
     * Find all invoices with all related entities eagerly loaded
     */
    @Query("SELECT i FROM Invoice i " +
           "LEFT JOIN FETCH i.client c " +
           "LEFT JOIN FETCH i.project p " +
           "LEFT JOIN FETCH p.bdm " +
           "LEFT JOIN FETCH p.managerSpoc " +
           "LEFT JOIN FETCH p.accountHeadSpoc " +
           "LEFT JOIN FETCH p.businessHeadSpoc " +
           "LEFT JOIN FETCH p.hrSpoc " +
           "LEFT JOIN FETCH p.financeSpoc " +
           "LEFT JOIN FETCH i.candidate cand " +
           "LEFT JOIN FETCH i.hsnCode " +
           "LEFT JOIN FETCH i.redberylAccount " +
           "LEFT JOIN FETCH i.invoiceType " +
           "LEFT JOIN FETCH i.staffingType")
    List<Invoice> findAllWithAllRelations();
}
