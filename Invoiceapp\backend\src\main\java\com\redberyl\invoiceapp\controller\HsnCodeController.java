package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.HsnCodeDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.HsnCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@Tag(name = "HSN Code", description = "HSN Code management API")
@CrossOrigin(origins = {"http://localhost:3000", "http://127.0.0.1:3000"}, allowedHeaders = "*", methods = {
        RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS
})
public class HsnCodeController {

    @Autowired
    private HsnCodeService hsnCodeService;

    @GetMapping("/hsn-codes/getAll")
    @Operation(summary = "Get all HSN codes", description = "Get all HSN codes")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "HSN codes found"),
            @ApiResponse(responseCode = "204", description = "No HSN codes found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<HsnCodeDto>> getAllHsnCodes() {
        try {
            List<HsnCodeDto> hsnCodes = hsnCodeService.getAllHsnCodes();
            return new ResponseEntity<>(hsnCodes, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/hsn-codes/getById/{id}")
    @Operation(summary = "Get HSN code by ID", description = "Get HSN code by ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<HsnCodeDto> getHsnCodeById(@PathVariable Long id) {
        HsnCodeDto hsnCode = hsnCodeService.getHsnCodeById(id);
        return new ResponseEntity<>(hsnCode, HttpStatus.OK);
    }

    @GetMapping("/hsn-codes/getByCode/{code}")
    @Operation(summary = "Get HSN code by code", description = "Get HSN code by code")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<HsnCodeDto> getHsnCodeByCode(@PathVariable String code) {
        HsnCodeDto hsnCode = hsnCodeService.getHsnCodeByCode(code);
        return new ResponseEntity<>(hsnCode, HttpStatus.OK);
    }

    @PostMapping("/hsn-codes/create")
    @Operation(summary = "Create HSN code", description = "Create HSN code")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<HsnCodeDto> createHsnCode(@Valid @RequestBody HsnCodeDto hsnCodeDto) {
        HsnCodeDto createdHsnCode = hsnCodeService.createHsnCode(hsnCodeDto);
        return new ResponseEntity<>(createdHsnCode, HttpStatus.CREATED);
    }

    @PutMapping("/hsn-codes/update/{id}")
    @Operation(summary = "Update HSN code", description = "Update HSN code")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<HsnCodeDto> updateHsnCode(@PathVariable Long id, @Valid @RequestBody HsnCodeDto hsnCodeDto) {
        HsnCodeDto updatedHsnCode = hsnCodeService.updateHsnCode(id, hsnCodeDto);
        return new ResponseEntity<>(updatedHsnCode, HttpStatus.OK);
    }

    @DeleteMapping("/hsn-codes/deleteById/{id}")
    @Operation(summary = "Delete HSN code", description = "Delete HSN code")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteHsnCode(@PathVariable Long id) {
        hsnCodeService.deleteHsnCode(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    // Simplified API endpoints for frontend integration

    @GetMapping("/hsn-codes")
    @Operation(summary = "Get all HSN codes (simplified)", description = "Get all HSN codes with simplified endpoint")
    public ResponseEntity<List<HsnCodeDto>> getAllHsnCodesSimplified() {
        try {
            List<HsnCodeDto> hsnCodes = hsnCodeService.getAllHsnCodes();
            return new ResponseEntity<>(hsnCodes, HttpStatus.OK);
        } catch (NoContentException e) {
            // Return empty list instead of 204 No Content
            return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
        }
    }

    @GetMapping("/hsn-codes/{id}")
    @Operation(summary = "Get HSN code by ID (simplified)", description = "Get HSN code by ID with simplified endpoint")
    public ResponseEntity<HsnCodeDto> getHsnCodeByIdSimplified(@PathVariable Long id) {
        try {
            HsnCodeDto hsnCode = hsnCodeService.getHsnCodeById(id);
            return new ResponseEntity<>(hsnCode, HttpStatus.OK);
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping("/hsn-codes")
    @Operation(summary = "Create HSN code (simplified)", description = "Create HSN code with simplified endpoint")
    public ResponseEntity<HsnCodeDto> createHsnCodeSimplified(@Valid @RequestBody HsnCodeDto hsnCodeDto) {
        try {
            HsnCodeDto createdHsnCode = hsnCodeService.createHsnCode(hsnCodeDto);
            return new ResponseEntity<>(createdHsnCode, HttpStatus.CREATED);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/noauth/hsn-codes")
    @Operation(summary = "Get all HSN codes (no auth)", description = "Get all HSN codes without authentication")
    public ResponseEntity<List<HsnCodeDto>> getAllHsnCodesNoAuth() {
        try {
            List<HsnCodeDto> hsnCodes = hsnCodeService.getAllHsnCodes();
            return new ResponseEntity<>(hsnCodes, HttpStatus.OK);
        } catch (NoContentException e) {
            // Return empty list instead of 204 No Content
            return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
        }
    }

    @PutMapping("/api/hsn-codes/{id}")
    @Operation(summary = "Update HSN code (simplified)", description = "Update HSN code with simplified endpoint")
    public ResponseEntity<HsnCodeDto> updateHsnCodeSimplified(@PathVariable Long id, @Valid @RequestBody HsnCodeDto hsnCodeDto) {
        try {
            HsnCodeDto updatedHsnCode = hsnCodeService.updateHsnCode(id, hsnCodeDto);
            return new ResponseEntity<>(updatedHsnCode, HttpStatus.OK);
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/api/hsn-codes/{id}")
    @Operation(summary = "Delete HSN code (simplified)", description = "Delete HSN code with simplified endpoint")
    public ResponseEntity<Void> deleteHsnCodeSimplified(@PathVariable Long id) {
        try {
            hsnCodeService.deleteHsnCode(id);
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }
}
